<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Page;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class PagesController extends Controller
{
    public function index()
    {
        \Log::error(request()->userAgent());
        return Cache::tags(['pages'])->remember('pages', 60 * 60 * 3, function () {
            return optional(Page::where('name', 'Home')->first())->load('media');
        });
    }

    public function show($slug, Page $page = null)
    {
        \Log::error(request()->userAgent());
        if (!$page) {
            $page = Page::where('slug', $slug)->firstOrFail();
        }

        if (str_slug($page->title) != $slug && !request()->header('app')) {
            abort(302, $page->path);
        }

        return Cache::tags(['pages'])->remember("page_{$page->id}", 60 * 60 * 24 * 7, function () use ($page) {
            return $page->load('media');
        });
    }

    public function findComponentByKey($components, $key)
    {
        if (!is_array($components)) {
            return null;
        }
        foreach ($components as $component) {
            if (isset($component['key'])) {
                if ($component['key'] === $key) {
                    return $component;
                }
                if (isset($component['attributes']) && is_array($component['attributes'])) {
                    foreach ($component['attributes'] as $attribute) {
                        if(is_array($attribute)){
                            $found = $this->findComponentByKey($attribute, $key);
                            if ($found) {
                                return $found;
                            }
                        }
                    }
                }
            }
        }

        return null;
    }
    public function getPageComponentById(Page $page, $componentId)
    {
        if(!$page) {
            abort(404, 'Page not found');
        }
        $components = collect($page->components)->toArray();
        $component = $this->findComponentByKey($components, $componentId);
        if (!$component) {
            abort(404, 'Component not found');
        }
        return response()->json($component);
    }
}
