<?php

namespace App\Observers;

use App\Bag;
use App\Http\Controllers\GoogleShoppingController;
use App\LinkItem;
use App\Order;
use App\Product;
use App\Services\KlaviyoService;
use App\Services\ProductKlaviyoService;
use App\VariationInfo;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use App\Http\Controllers\PosReportsController;
use App\Http\Controllers\Api\EmailsController;
use Laravel\Nova\Http\Requests\NovaRequest;
use Laravel\Nova\Nova;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use ZipArchive;

class ProductObserver
{
    public function saving(Product $product)
    {
        $request = request();
        $this->validateRequiredFields($request);
        if (filter_var($request->visibility, FILTER_VALIDATE_BOOLEAN) && !$request->has('__media__')) {
            abort(response()->json([
                'errors' => [
                    'media' => ['Media is required when setting visibility on.'],
                ],
                'message' => 'The given data was invalid.'
            ], 422));
        }
        // $product->search = $product->getModelSwiftypeTransformed();
        $product->slug = $this->make_slug($product);

        $this->getFromPOS($product);
    }

    public function saved(Product $product)
    {
        $this->updateVariations($product);
        $this->updateIds($product);
        $this->sendNotifications($product);

        $product->updateSearchFeild();
        $this->updateDigital($product);

        $this->updateSwiftype($product);

        $product->refreshCache();
        refreshCache();

        $request = request();
        if ($request->add_sale) {
            $product->sale()->updateOrCreate(['model_type' => 'App\Product', 'model_id' => $product->id], [
                'type' => $request->sale_type,
                'from' => $request->sale_from,
                'amount' => $request->sale_amount,
                'start' => $request->start_sale,
                'end' => $request->end_sale,
            ]);
        }

        if ($request->add_online_price == 'rules') {
            $product->onlinePrice()->updateOrCreate(['model_type' => 'App\Product', 'model_id' => $product->id], [
                'percent' => $request->online_price_percent,
                'based_on' => $request->online_price_based_on,
            ]);
        }

        // we anyway do a full sync 'php artisan send-products-to-google'
        // if ($product->searchable) {
        //     dispatch(function () use ($product) {
        //         GoogleShoppingController::addProduct($product);
        //     });
        // }

        $this->checkThatSkuIsUnique($product);

        if (!$product->wasRecentlyCreated) {
            try {
                if (config('klaviyo.enabled')) {
                    $klaviyo = new KlaviyoService();
                    $item = ProductKlaviyoService::convertProductToCatalogItem($product);
                    $klaviyo->updateCatalogItem($product->id, $item);
                }
            } catch (\Exception $e) {
                Log::error('Error updating product in Klaviyo', [
                    'product_id' => $product->id,
                    'error' => $e->getMessage(),
                ]);
            }
        }
    }

    public function checkThatSkuIsUnique($product)
    {
        if (Product::where('id', '!=', $product->id)->pluck('sku')->contains($product->sku)) {
            abort(500, 'You already have the sku ' . $product->sku . ' somewhere else...');
        }
    }

    public function created(Product $product)
    {
        Nova::whenServing(function (NovaRequest $request) use ($product) {
            $parent_id = $request->input('fromResourceId');
            if ($parent_id) {
                $parentProduct = Product::find($parent_id);
                if ($parentProduct) {
                    $media = $parentProduct->getMedia("*");
                    foreach ($media as $item) {
                        try{
                            $product->addMedia($item->getPath())->toMediaCollection($item->collection_name);
                        } catch (\Exception $e) {
                            Log::error('Error copying media', [$e->getMessage()]);
                        }
                    }
                    $product->save();
                }
            }
        });
        $this->getFromPOS($product);


        /*
         * If Klaviyo is enabled, create a catalog item in Klaviyo.
         */
        try {
            if (config('klaviyo.enabled')) {
                $klaviyo = new KlaviyoService();
                $item = ProductKlaviyoService::convertProductToCatalogItem($product);
                $klaviyo->createCatalogItem($item);
            }
        } catch (\Exception $e) {
            Log::error('Error creating product in Klaviyo', [
                'product_id' => $product->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    public function deleted(Product $product)
    {
        $this->updateSettings($product->id, 'delete');

        $product->filters()->sync([]);
        $product->creators()->sync([]);
        $product->recurringSettings()->sync([]);
        $product->categories()->sync([]);
        $product->variations()->delete();
        $product->variationInfos()->delete();

        LinkItem::where('model_type', 'App\\Product')
            ->where('model_id', $product->id)
            ->delete();


        Bag::where('model_type', 'App\\Product')
            ->where('model_id', $product->id)
            ->delete();

        /*
         * If Klaviyo is enabled, delete the catalog item in Klaviyo.
         */
        try {
            if (config('klaviyo.enabled')) {
                $klaviyo = new KlaviyoService();
                $klaviyo->deleteCatalogItem($product->id);
            }
        } catch (\Exception $e) {
            Log::error('Error deleting product in Klaviyo', [
                'product_id' => $product->id,
                'error' => $e->getMessage(),
            ]);
        }

        refreshCache();
    }

    public function updateIds($product)
    {
        $request = request();


        if ($request->has('category_ids')) { // From imports
            $product->categories()->sync(explode(',', $request->category_ids));
        }
        if ($request->has('filter_ids')) {
            $product->filters()->sync(explode(',', $request->filter_ids));
        }
        if ($request->has('categories_ids')) {
            $product->categories()->sync(explode(',', $request->categories_ids));
        }
        if ($request->has('creators_ids')) {
            $product->creators()->sync(explode(',', $request->creators_ids));
        }
        if ($request->has('recurrings')) {
            $product->recurringSettings()->sync(explode(',', $request->recurrings));
        }
    }

    public function updateVariations(&$product)
    {
        if (request()->has('variations')) {
            $ids = [];
            foreach (forceArray(request()->variations) as $key => $variation) {
                $data = [
                    'order' => $key + 1,
                    'name' => $variation['name'],
                    'images' => data_get($variation, 'images'),
                    'info' => isset($variation['info']) ? $variation['info'] : '',
                    'values' => isset($variation['values']) ? $variation['values'] : '',
                    'filter' => isset($variation['filter']) ? $variation['filter'] : '',
                    'assistance' => isset($variation['assistance']) ? $variation['assistance'] : '',
                ];

                $variation = $product->variations()->updateOrCreate(
                    ['values' => isset($variation['values']) ? $variation['values'] : ''],
                    $data
                );
                $ids[] = $variation->id;
            }

            $product = $product->fresh();
            $product->variations()->whereNotIn('id', $ids)->delete();

            $this->updateVariationInfos($product);
        }
    }

    public function updateVariationInfos($product)
    {
        $variations = $product->variationInfos;
        Log::debug('updateVariationInfos', [
            'variations' => $variations,
            'request' => request()->variation_info,
        ]);
        if (request()->variation_info) {
            $ids = [];
            $variation_info = collect(forceArray(request()->variation_info))->filter(function ($item) {
                return !empty($item['meta']);
            })->toArray();
            foreach ($variation_info as $key => $value) {
                $sale = null;
                if (data_get($value, 'add_sale')) {
                    $sale = [
                        'type' => $value['sale_type'],
                        'from' => $value['sale_from'],
                        'amount' => $value['sale_amount'],
                        'start' => $value['start_sale'],
                        'end' => $value['end_sale'],
                    ];
                }
                $online_price = null;
                if (data_get($value, 'add_online_price') == 'rules') {
                    $online_price = [
                        'percent' => $value['online_price_percent'],
                        'based_on' => $value['online_price_based_on'],
                    ];
                }

                unset($value['add_sale']);
                unset($value['sale_type']);
                unset($value['sale_from']);
                unset($value['sale_amount']);
                unset($value['start_sale']);
                unset($value['end_sale']);
                unset($value['track_link']);

                unset($value['add_online_price']);
                unset($value['online_price_percent']);
                unset($value['online_price_based_on']);

                unset($value['id']);
                unset($value['price']);
                unset($value['fake_price']);
                unset($value['media_urls']);
                unset($value['media']);
                unset($value['full_urls']);
                unset($value['images']);

                //update pricing so empty string should be null not zero
                $value['sale_price'] = $value['sale_price'] === '' ? null : $value['sale_price'];
                $value['online_price'] = $value['online_price'] === '' ? null : $value['online_price'];
                $value['list_price'] = $value['list_price'] === '' ? null : $value['list_price'];
                $value['max_quantity'] = data_get($value, 'max_quantity') === '' ? null : data_get(
                    $value,
                    'max_quantity'
                );

                $where = collect($value['meta'])->map(function ($item, $kkey) {
                    return ['meta->' . $kkey, $item];
                })->values()->toArray();


                $old = $variations->filter(function ($var) use ($value) {
                    return str_replace(' ', '', $var->meta) == str_replace(' ', '', json_encode($value['meta']));
                })->values()->first();
                // $old = $variations->where($where)->first();
                // foreach ($value['meta'] as $kkey => $item) {
                //     $old = $variations->firstWhere('meta->'.$kkey, $item);

                //     dump($old);
                // }
                $value['meta'] = json_encode($value['meta']);
                $value['product_json'] = $product->setAppends([])->getAttributes() + [
                        'path' => $product->path,
                        'price' => $product->price,
                        'media_urls' => $product->media_urls,
                        'vendor_name' => optional($product->vendor)->name,
                    ];
                // dump($old);
                if ($old/* = $old->first()*/) {
                    $old->update($value);
                } else {
                    $old = $product->variationInfos()->create($value);
                }

                if ($sale) {
                    $old->sale()->updateOrCreate(['model_type' => 'App\VariationInfo', 'model_id' => $old->id], $sale);
                } else {
                    $old->sale()->delete();
                }

                if ($online_price) {
                    $old->onlinePrice()->updateOrCreate(['model_type' => 'App\VariationInfo', 'model_id' => $old->id],
                        $online_price);
                } else {
                    $old->onlinePrice()->delete();
                }

                $ids[] = $old->id;
            }
            $product->hasMany(VariationInfo::class)->whereNotIn('id', $ids)->delete();
            $product->withoutEvents(function () use ($product) {
                $product->update([
                    'var_skus->skus' => $product->variationInfos->map(function ($var) {
                        return $var->sku;
                    })->filter()->values(),
                    'var_skus->quantity' => $product->variationInfos->map(function ($var) {
                        return $var->max;
                    })->filter()->sum()
                ]);
            });
        }
    }

    public function sendNotifications($product)
    {
        if (request()->notification === "true" && !data_get($product, 'notification.sent')) {
            $followers = $product->followers;

            $linkItem = $product->linkItems()->first();
            if($linkItem) {
                $linkedProductIds = $linkItem->link->linkItems()->where('model_type', Product::class)->pluck(
                    'model_id'
                );

                // Add linked products followers
                foreach ($linkedProductIds as $productId) {
                    $linkedProduct = Product::find($productId);
                    if ($linkedProduct) {
                        $followers = $followers->merge($linkedProduct->followers);
                    }
                }

                // Add linked products orders followers
                $customerIds = Order::where(function ($query) use ($linkedProductIds) {
                    foreach ($linkedProductIds as $productId) {
                        $query->orWhereJsonContains('products_ids', $productId);
                    }
                })->distinct()->pluck('customer_id');

                $customers = $product->customers()->whereIn('id', $customerIds)->get();

                $followers = $followers->merge($customers->map(function ($customer) {
                    return (object)[
                        'model' => null,
                        'customer' => $customer,
                    ];
                }));
            }

            $sent_to_ids = [];
            $followers->each(function ($follow) use ($product, &$sent_to_ids) {
                if (!in_array($follow->customer->id, $sent_to_ids)) {
                    EmailsController::SendFollowCreator($product, [$follow->model], $follow->customer);
                    $sent_to_ids[] = $follow->customer->id;
                }
            });

            $product->withoutEvents(function () use ($product, $followers) {
                $product->update([
                    'notification->sent' => true,
                    'notification->sent_on' => now()->format('Y-m-d H:i'),
                    'notification->amount' => $followers->count(),
                ]);
            });
        }
    }

    public function make_slug($product)
    {
        //not unique
        return Str::slug($product->title);

        $slug = Str::slug($product->title);

        $count = Product::whereRaw("slug RLIKE '^{$slug}(-[0-9]+)?$'")->count();

        return $count ? "{$slug}-{$count}" : $slug;
    }

    public function updateSwiftype($product)
    {
        if ($product->searchable) {
            $this->updateSettings($product->id, 'update');
        } else {
            $this->updateSettings($product->id, 'delete');
        }
    }

    public function updateSettings($id, $type)
    {
        if ($type == 'update') {
            $delete = collect(json_decode(settings()->getValue('swiftype_delete_ids')))
                ->filter(function ($item) use ($id) {
                    return $item != $id;
                })->values();
            settings()->setValue('swiftype_delete_ids', json_encode($delete));

            $ids = collect(json_decode(settings()->getValue('swiftype_update_ids')));
            settings()->setValue('swiftype_update_ids', json_encode($ids->push($id)->unique()));
        } else {
            $delete = collect(json_decode(settings()->getValue('swiftype_update_ids')))
                ->filter(function ($item) use ($id) {
                    return $item != $id;
                })->values();
            settings()->setValue('swiftype_update_ids', json_encode($delete));

            $ids = collect(json_decode(settings()->getValue('swiftype_delete_ids')));
            settings()->setValue('swiftype_delete_ids', json_encode($ids->push($id)->unique()));
        }
    }

    public function getFromPOS($product)
    {
        if (!$product->sku) {
            $product->store_price = null;
            $product->store_quantity = null;
            return;
        }
        if ($product->isDirty('sku') || $product->isDirty('visibility') || $product->wasRecentlyCreated) {
            $product->withoutEvents(function () use ($product) {
                PosReportsController::GetProductInfo($product);
            });
        }
    }

    public function updateDigital($product)
    {
        $value = json_decode(request()->digital);

        if (data_get($value, 'deleted')) {
            $product->clearMediaCollection('digital');
        } elseif (data_get($value, 'url')) {
            $product->clearMediaCollection('digital');

            $name = data_get($value, 'name');
            $media = Media::create([
                'collection_name' => 'digital',
                'name' => $name,
                'file_name' => $name,
                'order_column' => 1,
                'model_id' => $product->id,
                'model_type' => 'App\Product',
                'disk' => 'media',
                'responsive_images' => [],
                'manipulations' => [],
                'mime_type' => data_get($value, 'type'),
                'size' => data_get($value, 'size'),
            ]);
            Storage::disk('s3')
                ->move(
                    data_get($value, 'url'),
                    "media/{$media->id}/$name"
                );
            if ($media->mime_type == 'application/zip') {
                $zip = new ZipArchive;
                $url = $media->getTemporaryUrl(now()->addMinutes(5));

                $str = (string)Str::uuid();
                file_put_contents(public_path("{$str}.zip"), fopen($url, 'r'));
                $zip->open(public_path("{$str}.zip"));
                $array = [];
                for ($i = 0; $i < $zip->numFiles; $i++) {
                    $filename = $zip->getNameIndex($i);

                    $ext = strtoupper((pathinfo($filename, PATHINFO_EXTENSION)));
                    if ($ext && !collect($array)->contains($ext)) {
                        $array = array_merge($array, [$ext]);
                    }
                }
                $zip->close();
                unlink(public_path("{$str}.zip"));
                $media->update(['custom_properties->extensions' => $array]);
            } else {
                $media->update([
                    'custom_properties->extensions'
                    => [strtoupper((pathinfo($media->file_name, PATHINFO_EXTENSION)))]
                ]);
            }
        }
    }

    public function validateRequiredFields($request)
    {
        if ($request->is('nova-api/*')) {
            $fields = [
                'Width',
                'Height',
                'Length',
                'Weight'
            ];
            collect($fields)->each(function ($field) use ($request) {
                $str = strtolower($field);
                if ($request->exists($str) && !$request->$str) {
                    abort(500, $field . ' is required');
                }
            });
        }
    }
}
